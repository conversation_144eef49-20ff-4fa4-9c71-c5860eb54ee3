import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MainComponent } from './main.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { MainResolverService } from './main.resolver';

const routes: Routes = [
  {
    path: '',
    component: MainComponent,
    resolve: {
      initData: MainResolverService,
    },
    children: [
      {
        path: 'dashboard',
        component: DashboardComponent,
      },
      // {
      //   path: 'factory',
      //   loadChildren: () => import('../factory/factory.module').then((m) => m.FactoryModule),
      // },
      // {
      //   path: 'hr',
      //   loadChildren: () => import('../hr/hr.module').then((m) => m.HrModule),
      // },
      {
        path: 'order',
        loadChildren: () => import('../order/order.module').then((m) => m.OrderModule),
      },
      {
        path: 'settings',
        loadChildren: () => import('../settings/settings.module').then((m) => m.SettingsModule),
      },
      // {
      //   path: 'settings',
      //   loadChildren: () => import('fl-sewsmart-lib/basic-archive').then((m) => m.BasicArchiveModule),
      // },
      // {
      //   path: 'production-report',
      //   loadChildren: () => import('../production-report/production-report.module').then((m) => m.ProductionReportModule),
      // },
      {
        path: 'basic-archive',
        loadChildren: () => import('fl-sewsmart-lib/basic-archive').then((m) => m.BasicArchiveModule),
      },
      // {
      //   path: 'product-plan-archive',
      //   loadChildren: () => import('fl-sewsmart-lib/basic-archive').then((m) => m.BasicArchiveModule),
      // },
      // {
      //   path: 'dev-manage',
      //   loadChildren: () => import('fl-sewsmart-lib/dev-manage').then((m) => m.DevManageModule),
      // },
      // {
      //   path: 'product-plan-manage',
      //   loadChildren: () => import('fl-sewsmart-lib/product-plan-manage').then((m) => m.ProductPlanManageModule),
      // },
      {
        path: 'sample-manage',
        loadChildren: () => import('fl-sewsmart-lib/sample-manage').then((m) => m.SampleManageModule),
      },
      // {
      //   path: 'check-price-manage',
      //   loadChildren: () => import('fl-sewsmart-lib/check-price-manage').then((m) => m.CheckPriceManageModule),
      // },
      {
        path: 'style-center',
        loadChildren: () => import('fl-sewsmart-lib/style-center').then((m) => m.StyleCenterModule),
      },
      {
        path: 'sys-settings',
        loadChildren: () => import('../sys-settings/sys-settings.module').then((m) => m.SysSettingsModule),
      },
      {
        path: 'sys-settings',
        loadChildren: () => import('fl-sewsmart-lib/basic-archive').then((m) => m.BasicArchiveModule),
      },
      // {
      //   path: 'sys-settings',
      //   loadChildren: () => import('fl-sewsmart-lib/notification-setting').then((m) => m.NotificationSettingModule),
      // },
      // {
      //   path: 'tech-archive',
      //   loadChildren: () => import('fl-sewsmart-lib/tech-archive').then((m) => m.TechArchiveModule),
      // },
      // {
      //   path: 'intellect-plan',
      //   loadChildren: () => import('../plan/plan.module').then((m) => m.PlanModule),
      // },
      // // 发货管理 - 物料发货管理
      // {
      //   path: 'delivery-plan',
      //   loadChildren: () => import('fl-sewsmart-lib/material-delivery').then((m) => m.MaterialDeliveryModule),
      // },
      // //发货管理 - 成品装箱管理
      // {
      //   path: 'delivery-plan',
      //   loadChildren: () => import('fl-sewsmart-lib/packing').then((m) => m.PackingModule),
      // },
      // // 备料采购管理
      {
        path: 'material-procurement',
        loadChildren: () => import('fl-sewsmart-lib/procurement-v2').then((m) => m.ProcurementV2Module),
      },
      // // 物料库存
      {
        path: 'material-inventory',
        loadChildren: () => import('fl-sewsmart-lib/inventory-v2').then((m) => m.InventoryV2Module),
      },
      // // 物料库存 - 物料收发
      // {
      //   path: 'material-inventory',
      //   loadChildren: () => import('fl-sewsmart-lib/material-delivery').then((m) => m.MaterialDeliveryModule),
      // },
      // // 物料供应商协同
      // {
      //   path: 'material-supply-management',
      //   loadChildren: () => import('fl-sewsmart-lib/material-supply-management').then((m) => m.MaterialSupplyManagementModule),
      // },
      // // 物料供应商协同 - 物料收发
      // {
      //   path: 'material-supply-management',
      //   loadChildren: () => import('fl-sewsmart-lib/material-delivery').then((m) => m.MaterialDeliveryModule),
      // },
      // // 物料供应商协同 - 供应商绩效
      // {
      //   path: 'material-supply-management',
      //   loadChildren: () => import('fl-sewsmart-lib/supplier-management').then((m) => m.SupplierManagementModule),
      // },
      // // 物料供应商协同 - 采购合同
      // {
      //   path: 'material-supply-management',
      //   loadChildren: () => import('fl-sewsmart-lib/contract-management').then((m) => m.ContractManagementModule),
      // },
      // // 外发管理
      {
        path: 'outsourcing-manage',
        loadChildren: () => import('../outsourcing-manage/outsourcing-manage.module').then((m) => m.OutsourcingManageModule),
      },
      // {
      //   path: 'quote-price-manage',
      //   loadChildren: () => import('fl-sewsmart-lib/inquiry-quote-price-manage').then((m) => m.InquiryQuotePriceManageModule),
      // },
      // // 财务管理
      {
        path: 'settlement',
        loadChildren: () => import('fl-sewsmart-lib/settlement').then((m) => m.SettlementModule),
      },
      // // 质检管理
      // {
      //   path: 'quality-control-manage',
      //   loadChildren: () => import('fl-sewsmart-lib/quality-control-manage').then((m) => m.QualityControlManageModule),
      // },
      // // 成品采购
      // {
      //   path: 'product-procurement',
      //   loadChildren: () => import('fl-sewsmart-lib/product-procurement').then((m) => m.ProductProcurementModule),
      // },
      // // 成品库存
      {
        path: 'product-inventory',
        loadChildren: () => import('fl-sewsmart-lib/product-inventory').then((m) => m.ProductInventoryModule),
      },
      // {
      //   path: 'supplier-management',
      //   loadChildren: () => import('fl-sewsmart-lib/supplier-management').then((m) => m.SupplierManagementModule),
      // },
      // // 供应商管理（供应商类型、加工厂档案、供应商档案）
      // {
      //   path: 'supplier-management',
      //   loadChildren: () => import('fl-sewsmart-lib/basic-archive').then((m) => m.BasicArchiveModule),
      // },
      // // 合同管理
      {
        path: 'contract-management',
        loadChildren: () => import('fl-sewsmart-lib/contract-management').then((m) => m.ContractManagementModule),
      },
      // // 样衣管理
      // {
      //   path: 'sample-dress-management',
      //   loadChildren: () => import('fl-sewsmart-lib/sample-dress-management').then((m) => m.sampleDressManagementModule),
      // },
      // // 合格证管理
      // {
      //   path: 'production-management',
      //   loadChildren: () => import('fl-sewsmart-lib/production-management').then((m) => m.ProductionManagementModule),
      // },
      //  //板样管理
      // {
      //   path: 'board-material-manage',
      //   loadChildren: () => import('fl-sewsmart-lib/board-material-manage').then((m) => m.BoardMaterialManageModule),
      // },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class MainRoutingModule {}
